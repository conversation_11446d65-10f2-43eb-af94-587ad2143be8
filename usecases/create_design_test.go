package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// Mock presenter for creation outcome
type mockCreationOutcomePresenter struct {
	mock.Mock
}

func (m *mockCreationOutcomePresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockCreationOutcomePresenter) ConveySuccessWithNewResourceLocation(projectId *entities.ProjectId, designId *uuid.UUID) {
	m.Called(projectId, designId)
}

func TestNewDesignCreater(t *testing.T) {
	t.Run("should create creater with valid repository", func(t *testing.T) {
		repo := &mockDesignRepository{}
		creater := usecases.NewDesignCreater(repo)
		assert.NotNil(t, creater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignCreater(nil)
		})
	})
}

func TestDesignCreater_CreateDesign(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	testDesign := usecases.Design{
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}

	t.Run("should create design successfully", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockCreationOutcomePresenter{}
		creater := usecases.NewDesignCreater(repo)

		newDesignId := uuid.New()
		repo.On("UpsertDesign", ctx, testDesign).Return(newDesignId, nil)
		presenter.On("ConveySuccessWithNewResourceLocation", &projectId, &newDesignId).Return()

		creater.CreateDesign(ctx, presenter, testDesign)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockCreationOutcomePresenter{}
		creater := usecases.NewDesignCreater(repo)

		dbError := errors.New("database constraint violation")
		repo.On("UpsertDesign", ctx, testDesign).Return(uuid.UUID{}, dbError)
		presenter.On("PresentError", dbError).Return()

		creater.CreateDesign(ctx, presenter, testDesign)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle design with existing ID", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockCreationOutcomePresenter{}
		creater := usecases.NewDesignCreater(repo)

		existingId := uuid.New()
		designWithId := testDesign
		designWithId.ID = existingId

		// The repository should return the same ID when upserting
		repo.On("UpsertDesign", ctx, designWithId).Return(existingId, nil)
		presenter.On("ConveySuccessWithNewResourceLocation", &projectId, &existingId).Return()

		creater.CreateDesign(ctx, presenter, designWithId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle design with all optional fields", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockCreationOutcomePresenter{}
		creater := usecases.NewDesignCreater(repo)

		// Create a design with all optional fields populated
		faucetId := uuid.New()
		floorTileId := uuid.New()
		lightingId := uuid.New()
		mirrorId := uuid.New()
		paintId := uuid.New()
		toiletId := uuid.New()
		vanityId := uuid.New()

		fullDesign := usecases.Design{
			ProjectID:          projectId,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				ColorScheme:      &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:            &[]usecases.Style{usecases.Modern}[0],
				FloorTilePattern: &[]usecases.TilePattern{usecases.Herringbone}[0],
				Faucet:           &faucetId,
				FloorTile:        &floorTileId,
				Lighting:         &lightingId,
				Mirror:           &mirrorId,
				Paint:            &paintId,
				Toilet:           &toiletId,
				Vanity:           &vanityId,
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     true,
			NichesVisible:      true,
		}

		newDesignId := uuid.New()
		repo.On("UpsertDesign", ctx, fullDesign).Return(newDesignId, nil)
		presenter.On("ConveySuccessWithNewResourceLocation", &projectId, &newDesignId).Return()

		creater.CreateDesign(ctx, presenter, fullDesign)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle different design statuses", func(t *testing.T) {
		statuses := []usecases.DesignStatus{
			usecases.Preview,
			usecases.Fave,
			usecases.Archived,
		}

		for _, status := range statuses {
			t.Run("status_"+string(status), func(t *testing.T) {
				repo := &mockDesignRepository{}
				presenter := &mockCreationOutcomePresenter{}
				creater := usecases.NewDesignCreater(repo)

				design := testDesign
				design.Status = status

				newDesignId := uuid.New()
				repo.On("UpsertDesign", ctx, design).Return(newDesignId, nil)
				presenter.On("ConveySuccessWithNewResourceLocation", &projectId, &newDesignId).Return()

				creater.CreateDesign(ctx, presenter, design)

				repo.AssertExpectations(t)
				presenter.AssertExpectations(t)
			})
		}
	})
}
