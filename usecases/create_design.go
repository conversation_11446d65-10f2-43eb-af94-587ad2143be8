package usecases

import (
	"context"
)

type DesignCreater struct {
	designRepo designRepository
}

func NewDesignCreater(designRepo designRepository) *DesignCreater {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignCreater{designRepo: designRepo}
}

func (dc *DesignCreater) CreateDesign(ctx context.Context, presenter CreationOutcomePresenter, design Design) {
	var err error
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccessWithNewResourceLocation(&design.ProjectID, &design.ID)
}
