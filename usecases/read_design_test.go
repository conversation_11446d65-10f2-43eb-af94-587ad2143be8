package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// Mock repository for testing
type mockDesignRepository struct {
	mock.Mock
}

func (m *mockDesignRepository) ReadDesign(ctx context.Context, designId uuid.UUID) (usecases.Design, error) {
	args := m.Called(ctx, designId)
	return args.Get(0).(usecases.Design), args.Error(1)
}

func (m *mockDesignRepository) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]usecases.Design, error) {
	args := m.Called(ctx, projectId)
	return args.Get(0).([]usecases.Design), args.Error(1)
}

func (m *mockDesignRepository) DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]usecases.Design, []error, error) {
	args := m.Called(ctx, projectIDs)
	return args.Get(0).(map[entities.ProjectId][]usecases.Design), args.Get(1).([]error), args.Error(2)
}

func (m *mockDesignRepository) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	args := m.Called(ctx, design)
	return args.Get(0).(uuid.UUID), args.Error(1)
}

func (m *mockDesignRepository) DeleteDesign(ctx context.Context, designId uuid.UUID) error {
	args := m.Called(ctx, designId)
	return args.Error(0)
}

// Mock presenter for testing
type mockDesignsPresenter struct {
	mock.Mock
}

func (m *mockDesignsPresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockDesignsPresenter) PresentData(ctx context.Context, data any) {
	m.Called(ctx, data)
}

func (m *mockDesignsPresenter) PresentDesign(ctx context.Context, design usecases.Design) {
	m.Called(ctx, design)
}

func (m *mockDesignsPresenter) PresentDesigns(ctx context.Context, designs []usecases.Design) {
	m.Called(ctx, designs)
}

func (m *mockDesignsPresenter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	m.Called(ctx, data, errors)
}

func TestNewDesignRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := &mockDesignRepository{}
		retriever := usecases.NewDesignRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignRetriever(nil)
		})
	})
}

func TestDesignRetriever_RetrieveDesign(t *testing.T) {
	ctx := context.Background()
	designId := uuid.New()
	testDesign := usecases.Design{
		ID:        designId,
		ProjectID: "TEST-PROJECT",
		Status:    usecases.Preview,
	}

	t.Run("should present design when found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		repo.On("ReadDesign", ctx, designId).Return(testDesign, nil)
		presenter.On("PresentDesign", ctx, testDesign).Return()

		retriever.RetrieveDesign(ctx, presenter, designId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design not found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		repo.On("ReadDesign", ctx, designId).Return(usecases.Design{}, usecases.ErrNotFound)
		presenter.On("PresentError", usecases.ErrNotFound).Return()

		retriever.RetrieveDesign(ctx, presenter, designId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		dbError := errors.New("database connection failed")
		repo.On("ReadDesign", ctx, designId).Return(usecases.Design{}, dbError)
		presenter.On("PresentError", dbError).Return()

		retriever.RetrieveDesign(ctx, presenter, designId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}

func TestDesignRetriever_RetrieveAllDesignsForProject(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")
	testDesigns := []usecases.Design{
		{ID: uuid.New(), ProjectID: projectId, Status: usecases.Preview},
		{ID: uuid.New(), ProjectID: projectId, Status: usecases.Fave},
	}

	t.Run("should present designs when found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		repo.On("DesignsForProject", ctx, projectId).Return(testDesigns, nil)
		presenter.On("PresentDesigns", ctx, testDesigns).Return()

		retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present empty list when no designs found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		emptyDesigns := []usecases.Design{}
		repo.On("DesignsForProject", ctx, projectId).Return(emptyDesigns, nil)
		presenter.On("PresentDesigns", ctx, emptyDesigns).Return()

		retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		dbError := errors.New("database connection failed")
		repo.On("DesignsForProject", ctx, projectId).Return([]usecases.Design{}, dbError)
		presenter.On("PresentError", dbError).Return()

		retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}

func TestDesignRetriever_RetrieveDesignsForMultipleProjects(t *testing.T) {
	ctx := context.Background()
	projectIds := []entities.ProjectId{"PROJECT-1", "PROJECT-2"}
	designsByProject := map[entities.ProjectId][]usecases.Design{
		"PROJECT-1": {
			{ID: uuid.New(), ProjectID: "PROJECT-1", Status: usecases.Preview},
		},
		"PROJECT-2": {
			{ID: uuid.New(), ProjectID: "PROJECT-2", Status: usecases.Fave},
		},
	}

	t.Run("should present designs by project when found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		var noErrors []error
		repo.On("DesignsByProject", ctx, projectIds).Return(designsByProject, noErrors, nil)
		presenter.On("PresentDesignsByProject", ctx, designsByProject, noErrors).Return()

		retriever.RetrieveDesignsForMultipleProjects(ctx, presenter, projectIds)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present designs with partial errors", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		partialErrors := []error{errors.New("project not found")}
		repo.On("DesignsByProject", ctx, projectIds).Return(designsByProject, partialErrors, nil)
		presenter.On("PresentDesignsByProject", ctx, designsByProject, partialErrors).Return()

		retriever.RetrieveDesignsForMultipleProjects(ctx, presenter, projectIds)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockDesignsPresenter{}
		retriever := usecases.NewDesignRetriever(repo)

		dbError := errors.New("database connection failed")
		repo.On("DesignsByProject", ctx, projectIds).Return(map[entities.ProjectId][]usecases.Design{}, []error{}, dbError)
		presenter.On("PresentError", dbError).Return()

		retriever.RetrieveDesignsForMultipleProjects(ctx, presenter, projectIds)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}
