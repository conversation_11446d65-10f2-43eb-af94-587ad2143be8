package usecases_test

import (
	"context"
	"errors"
	"log/slog"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func TestNewBulkDesignSaver(t *testing.T) {
	t.Run("should create bulk saver with valid repository and logger", func(t *testing.T) {
		repo := &mockDesignRepository{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)
		assert.NotNil(t, saver)
	})

	t.Run("should create bulk saver with nil logger (uses default)", func(t *testing.T) {
		repo := &mockDesignRepository{}
		saver := usecases.NewBulkDesignSaver(repo, nil)
		assert.NotNil(t, saver)
	})
}

func TestBulkDesignSaver_SaveDesigns(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	t.Run("should save multiple designs successfully", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		designs := []usecases.Design{
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Preview,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
		}

		// Mock successful saves for both designs
		repo.On("UpsertDesign", ctx, designs[0]).Return(designs[0].ID, nil)
		repo.On("UpsertDesign", ctx, designs[1]).Return(designs[1].ID, nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should generate IDs for designs with zero UUID", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		var zeroUUID uuid.UUID
		designs := []usecases.Design{
			{
				ID:                 zeroUUID, // Zero UUID should get new ID
				ProjectID:          projectId,
				Status:             usecases.Fave, // Will be overridden to Preview
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
		}

		// The design should be modified to have a new ID and Preview status
		repo.On("UpsertDesign", ctx, mock.MatchedBy(func(design usecases.Design) bool {
			return design.ID != zeroUUID && design.Status == usecases.Preview
		})).Return(uuid.New(), nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should generate IDs for designs with uuid.Nil", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		designs := []usecases.Design{
			{
				ID:                 uuid.Nil, // uuid.Nil should get new ID
				ProjectID:          projectId,
				Status:             usecases.Fave, // Will be overridden to Preview
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
		}

		// The design should be modified to have a new ID and Preview status
		repo.On("UpsertDesign", ctx, mock.MatchedBy(func(design usecases.Design) bool {
			return design.ID != uuid.Nil && design.Status == usecases.Preview
		})).Return(uuid.New(), nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when any design fails to save", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		designs := []usecases.Design{
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Preview,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
		}

		// First design saves successfully, second fails
		repo.On("UpsertDesign", ctx, designs[0]).Return(designs[0].ID, nil)
		dbError := errors.New("database constraint violation")
		repo.On("UpsertDesign", ctx, designs[1]).Return(uuid.UUID{}, dbError)
		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when multiple designs fail to save", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		designs := []usecases.Design{
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Preview,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 uuid.New(),
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
		}

		// Both designs fail to save
		dbError1 := errors.New("database constraint violation")
		dbError2 := errors.New("foreign key constraint violation")
		repo.On("UpsertDesign", ctx, designs[0]).Return(uuid.UUID{}, dbError1)
		repo.On("UpsertDesign", ctx, designs[1]).Return(uuid.UUID{}, dbError2)
		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle empty design list", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		designs := []usecases.Design{}

		presenter.On("ConveySuccess").Return()

		saver.SaveDesigns(ctx, presenter, designs)

		// No repository calls should be made
		repo.AssertNotCalled(t, "UpsertDesign")
		presenter.AssertExpectations(t)
	})

	t.Run("should handle mixed valid and invalid IDs", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		logger := slog.Default()
		saver := usecases.NewBulkDesignSaver(repo, logger)

		var zeroUUID uuid.UUID
		designs := []usecases.Design{
			{
				ID:                 uuid.New(), // Valid ID, should remain unchanged
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.NoWallpaper,
				WallTilePlacement:  usecases.NoWallTile,
			},
			{
				ID:                 zeroUUID, // Zero UUID, should get new ID and Preview status
				ProjectID:          projectId,
				Status:             usecases.Archived,
				WallpaperPlacement: usecases.VanityWall,
				WallTilePlacement:  usecases.HalfWall,
			},
			{
				ID:                 uuid.Nil, // uuid.Nil, should get new ID and Preview status
				ProjectID:          projectId,
				Status:             usecases.Fave,
				WallpaperPlacement: usecases.AllWalls,
				WallTilePlacement:  usecases.FullWall,
			},
		}

		// First design should save as-is
		repo.On("UpsertDesign", ctx, designs[0]).Return(designs[0].ID, nil)
		
		// Second design should have new ID and Preview status
		repo.On("UpsertDesign", ctx, mock.MatchedBy(func(design usecases.Design) bool {
			return design.ID != zeroUUID && design.Status == usecases.Preview
		})).Return(uuid.New(), nil)
		
		// Third design should have new ID and Preview status
		repo.On("UpsertDesign", ctx, mock.MatchedBy(func(design usecases.Design) bool {
			return design.ID != uuid.Nil && design.Status == usecases.Preview
		})).Return(uuid.New(), nil)
		
		presenter.On("ConveySuccess").Return()

		saver.SaveDesigns(ctx, presenter, designs)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}
