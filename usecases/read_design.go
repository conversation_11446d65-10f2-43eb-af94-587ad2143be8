package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type DesignRetriever struct {
	designRepo designRepositoryReplica
}

func NewDesignRetriever(designRepo designRepositoryReplica) *DesignRetriever {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignRetriever{designRepo: designRepo}
}

func (dr *DesignRetriever) RetrieveDesign(ctx context.Context, presenter DesignsPresenter, designId uuid.UUID) {
	design, err := dr.designRepo.ReadDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesign(ctx, design)
}

func (dr *DesignRetriever) RetrieveAllDesignsForProject(ctx context.Context, presenter DesignsPresenter, projectId entities.ProjectId) {
	designs, err := dr.designRepo.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesigns(ctx, designs)
}

func (dr *DesignRetriever) RetrieveDesignsForMultipleProjects(ctx context.Context, presenter DesignsPresenter, projectIds []entities.ProjectId) {
	designsByProject, errors, err := dr.designRepo.DesignsByProject(ctx, projectIds)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesignsByProject(ctx, designsByProject, errors)
}
