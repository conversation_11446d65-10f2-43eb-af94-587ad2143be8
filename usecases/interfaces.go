package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type designRepositoryReplica interface {
	ReadDesign(ctx context.Context, designId uuid.UUID) (Design, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]Design, error)
	DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]Design, []error, error)
}

type designRepository interface {
	designRepositoryReplica
	UpsertDesign(ctx context.Context, design Design) (uuid.UUID, error)
	DeleteDesign(ctx context.Context, designId uuid.UUID) error
}

type Presenter interface {
	PresentError(err error)
}

type DesignsPresenter interface {
	Presenter
	PresentData(ctx context.Context, data any)
	PresentDesign(ctx context.Context, design Design)
	PresentDesigns(ctx context.Context, designs []Design)
	PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]Design, errors []error)
}

type CreationOutcomePresenter interface {
	Presenter
	ConveySuccessWithNewResourceLocation(projectId *entities.ProjectId, designId *uuid.UUID)
}

type MutationOutcomePresenter interface {
	CreationOutcomePresenter
	ConveySuccess()
}
