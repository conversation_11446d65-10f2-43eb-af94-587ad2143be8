package usecases

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type DesignDeleter struct {
	designRepo designRepository
}

func NewDesignDeleter(designRepo designRepository) *DesignDeleter {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignDeleter{designRepo: designRepo}
}

func (du *DesignDeleter) DeleteDesign(ctx context.Context, presenter MutationOutcomePresenter, designId uuid.UUID) {
	var zeroUUID uuid.UUID
	if designId == zeroUUID || designId == uuid.Nil {
		log.Println("Attempt to delete design with unspecified ID")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	du.designRepo.DeleteDesign(ctx, designId)
	presenter.ConveySuccess()
}
