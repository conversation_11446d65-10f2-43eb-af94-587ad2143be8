package usecases_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func TestNewDesignUpdater(t *testing.T) {
	t.Run("should create updater with valid repository", func(t *testing.T) {
		repo := &mockDesignRepository{}
		updater := usecases.NewDesignUpdater(repo)
		assert.NotNil(t, updater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignUpdater(nil)
		})
	})
}

func TestDesignUpdater_UpdateDesign(t *testing.T) {
	ctx := context.Background()
	designId := uuid.New()
	projectId := entities.ProjectId("TEST-PROJECT")

	existingDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
			Style:       &[]usecases.Style{usecases.Traditional}[0],
		},
		ShowerGlassVisible: false,
		TubDoorVisible:     false,
		NichesVisible:      false,
	}

	t.Run("should update design successfully", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		updater := usecases.NewDesignUpdater(repo)

		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]

		repo.On("ReadDesign", ctx, designId).Return(existingDesign, nil)

		// Use mock.AnythingOfType because MergeDesigns updates LastUpdated field
		repo.On("UpsertDesign", ctx, mock.AnythingOfType("usecases.Design")).Return(designId, nil)
		presenter.On("ConveySuccess").Return()

		updater.UpdateDesign(ctx, presenter, diff)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design ID is zero UUID", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		updater := usecases.NewDesignUpdater(repo)

		var zeroUUID uuid.UUID
		diff := usecases.DesignDiff{
			ID:     zeroUUID,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		updater.UpdateDesign(ctx, presenter, diff)

		repo.AssertNotCalled(t, "ReadDesign")
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design ID is uuid.Nil", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		updater := usecases.NewDesignUpdater(repo)

		diff := usecases.DesignDiff{
			ID:     uuid.Nil,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		updater.UpdateDesign(ctx, presenter, diff)

		repo.AssertNotCalled(t, "ReadDesign")
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design not found", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		updater := usecases.NewDesignUpdater(repo)

		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		repo.On("ReadDesign", ctx, designId).Return(usecases.Design{}, usecases.ErrNotFound)
		presenter.On("PresentError", usecases.ErrNotFound).Return()

		updater.UpdateDesign(ctx, presenter, diff)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when upsert fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		updater := usecases.NewDesignUpdater(repo)

		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		repo.On("ReadDesign", ctx, designId).Return(existingDesign, nil)

		dbError := errors.New("database constraint violation")
		repo.On("UpsertDesign", ctx, mock.AnythingOfType("usecases.Design")).Return(uuid.UUID{}, dbError)
		presenter.On("PresentError", dbError).Return()

		updater.UpdateDesign(ctx, presenter, diff)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}

func TestMergeDesigns(t *testing.T) {
	designId := uuid.New()
	projectId := entities.ProjectId("TEST-PROJECT")

	faucetId := uuid.New()
	floorTileId := uuid.New()

	baseDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			ColorScheme:      &[]usecases.ColorScheme{usecases.Neutral}[0],
			Style:            &[]usecases.Style{usecases.Traditional}[0],
			FloorTilePattern: &[]usecases.TilePattern{usecases.HorizontalStacked}[0],
			Title:            sql.NullString{String: "Original Title", Valid: true},
			Description:      sql.NullString{String: "Original Description", Valid: true},
			Faucet:           &faucetId,
			FloorTile:        &floorTileId,
		},
		ShowerGlassVisible: false,
		TubDoorVisible:     false,
		NichesVisible:      false,
	}

	t.Run("should merge status field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Fave}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.Fave, result.Status)
		// Other fields should remain unchanged
		assert.Equal(t, baseDesign.WallpaperPlacement, result.WallpaperPlacement)
		assert.Equal(t, baseDesign.ColorScheme, result.ColorScheme)
	})

	t.Run("should merge wallpaper placement field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			WallpaperPlacement: &[]usecases.WallpaperPlacement{usecases.VanityWall}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.VanityWall, result.WallpaperPlacement)
		assert.Equal(t, baseDesign.Status, result.Status) // Should remain unchanged
	})

	t.Run("should merge wall tile placement field", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                designId,
			WallTilePlacement: &[]usecases.WallTilePlacement{usecases.HalfWall}[0],
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.HalfWall, result.WallTilePlacement)
	})

	t.Run("should merge design options fields", func(t *testing.T) {
		newFaucet := uuid.New()
		newFloorTile := uuid.New()

		diff := usecases.DesignDiff{
			ID: designId,
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]
		diff.Style = &[]usecases.Style{usecases.Modern}[0]
		diff.FloorTilePattern = &[]usecases.TilePattern{usecases.Herringbone}[0]
		diff.Title = sql.NullString{String: "New Title", Valid: true}
		diff.Description = sql.NullString{String: "New Description", Valid: true}
		diff.Faucet = &newFaucet
		diff.FloorTile = &newFloorTile

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.Equal(t, usecases.Bold, *result.ColorScheme)
		assert.Equal(t, usecases.Modern, *result.Style)
		assert.Equal(t, usecases.Herringbone, *result.FloorTilePattern)
		assert.Equal(t, "New Title", result.Title.String)
		assert.Equal(t, "New Description", result.Description.String)
		assert.Equal(t, newFaucet, *result.Faucet)
		assert.Equal(t, newFloorTile, *result.FloorTile)
	})

	t.Run("should merge boolean visibility fields", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			ShowerGlassVisible: sql.NullBool{Bool: true, Valid: true},
			TubDoorVisible:     sql.NullBool{Bool: true, Valid: true},
			NichesVisible:      sql.NullBool{Bool: true, Valid: true},
		}

		result := usecases.MergeDesigns(baseDesign, diff)
		assert.True(t, result.ShowerGlassVisible)
		assert.True(t, result.TubDoorVisible)
		assert.True(t, result.NichesVisible)
	})

	t.Run("should not merge nil or invalid fields", func(t *testing.T) {
		diff := usecases.DesignDiff{
			ID:                 designId,
			Status:             nil,                        // Should not merge
			ShowerGlassVisible: sql.NullBool{Valid: false}, // Should not merge
		}
		// Don't set ColorScheme or Title - they should remain nil/invalid
		diff.Title = sql.NullString{Valid: false} // Should not merge

		result := usecases.MergeDesigns(baseDesign, diff)
		// All fields should remain unchanged
		assert.Equal(t, baseDesign.Status, result.Status)
		assert.Equal(t, baseDesign.ColorScheme, result.ColorScheme)
		assert.Equal(t, baseDesign.Title, result.Title)
		assert.Equal(t, baseDesign.ShowerGlassVisible, result.ShowerGlassVisible)
	})

	t.Run("should handle partial merge", func(t *testing.T) {
		// Only update some fields, leave others unchanged
		diff := usecases.DesignDiff{
			ID:     designId,
			Status: &[]usecases.DesignStatus{usecases.Archived}[0],
		}
		diff.ColorScheme = &[]usecases.ColorScheme{usecases.Bold}[0]
		// Don't update Style, FloorTilePattern, etc.

		result := usecases.MergeDesigns(baseDesign, diff)
		// Updated fields
		assert.Equal(t, usecases.Archived, result.Status)
		assert.Equal(t, usecases.Bold, *result.ColorScheme)
		// Unchanged fields
		assert.Equal(t, baseDesign.Style, result.Style)
		assert.Equal(t, baseDesign.FloorTilePattern, result.FloorTilePattern)
		assert.Equal(t, baseDesign.WallpaperPlacement, result.WallpaperPlacement)
	})
}
