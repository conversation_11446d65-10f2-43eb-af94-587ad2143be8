image: golang:latest

before_script:
  - go mod tidy
  - go mod download

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS && $CI_PIPELINE_SOURCE == "push"
      when: never
    - if: $CI_COMMIT_BRANCH

stages:
  - test
  - build
  - deploy

format:
  stage: test
  script:
    - go fmt $(go list ./...)
    - go vet $(go list ./...)

test:
  stage: test
  script:
    - go test -race $(go list ./...)

include:
  - project: "arc-studio-ai/packages/ci-templates"
    ref: main
    file: "/Builder.gitlab-ci.yml"

variables:
  AWS_ECS_CLUSTER_NAME: ServicesCluster

build_image:
  extends: .build-image

compile:
  stage: build
  script:
    - apt update
    - apt install unzip
    - go build -race -ldflags "-extldflags '-static'" -o $CI_PROJECT_DIR/service
  artifacts:
    paths:
      - service

deploy_prod:
  extends: .deploy-prod
