package web

import (
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type QueryHandler struct {
	logger     *slog.Logger
	controller *controllers.DesignRetrievalController
}

func NewQueryHandler(logger *slog.Logger, c *controllers.DesignRetrievalController) *QueryHandler {
	if c == nil {
		panic("controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &QueryHandler{logger: logger, controller: c}
}

func (h *QueryHandler) HandleListDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	projectId := r.PathValue("projectId")
	h.controller.FetchAllDesignsForProject(ctx, entities.NewProjectId(projectId), dp)
}

func (h *QueryHandler) HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	dp := presenters.NewDesignsPresenter(h.logger, w)
	h.controller.FetchDesign(ctx, designUUID, dp)
}

func (h *QueryHandler) HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	log.Println("URL query: ", r.URL.Query())
	h.logger.DebugContext(ctx, "Handling GET request for multiple projects...", slog.String("url", r.URL.RawQuery))
	projectIdsParam := r.URL.Query().Get("ids")
	if projectIdsParam == "" {
		http.Error(w, "No project IDs provided", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Designs for multiple projects requested", slog.String("projectIds", projectIdsParam))
	var projectIds []entities.ProjectId
	for id := range strings.SplitSeq(projectIdsParam, ",") {
		projectId := entities.NewProjectId(id)
		if !projectId.IsValid() {
			http.Error(w, fmt.Sprintf("Invalid project ID: '%v'.", projectId.String()), http.StatusBadRequest)
			return
		}
		projectIds = append(projectIds, projectId)
	}
	h.logger.InfoContext(ctx, "Fetching designs for multiple projects", slog.Int("count", len(projectIds)))
	h.controller.FetchDesignsForMultipleProjects(ctx, projectIds, dp)
}
