package web

import (
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type HttpGetReqHandler struct {
	logger     *slog.Logger
	controller *controllers.DesignAccessController
}

func NewHttpGetReqHandler(logger *slog.Logger, c *controllers.DesignAccessController) *HttpGetReqHandler {
	if c == nil {
		panic("controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &HttpGetReqHandler{logger: logger, controller: c}
}

func (h *HttpGetReqHandler) HandleListDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	projectId := r.PathValue("projectId")
	h.controller.FetchAllDesignsForProject(ctx, entities.NewProjectId(projectId), dp)
}

func (h *HttpGetReqHandler) HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	dp := presenters.NewDesignsPresenter(h.logger, w)
	projectId := r.PathValue("projectId")
	h.controller.FetchDesign(ctx, entities.NewProjectId(projectId), designUUID, dp)
}

func (h *HttpGetReqHandler) HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	log.Println("URL query: ", r.URL.Query())
	h.logger.DebugContext(ctx, "Handling GET request for multiple projects...", slog.String("url", r.URL.RawQuery))
	projectIdsParam := r.URL.Query().Get("ids")
	if projectIdsParam == "" {
		http.Error(w, "No project IDs provided", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Designs for multiple projects requested", slog.String("projectIds", projectIdsParam))
	var projectIds []entities.ProjectId
	for id := range strings.SplitSeq(projectIdsParam, ",") {
		projectId := entities.NewProjectId(id)
		if !projectId.IsValid() {
			http.Error(w, fmt.Sprintf("Invalid project ID: '%v'.", projectId.String()), http.StatusBadRequest)
			return
		}
		projectIds = append(projectIds, projectId)
	}
	h.controller.FetchDesignsForMultipleProjects(ctx, projectIds, dp)
}
