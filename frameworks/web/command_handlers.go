package web

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"

	"github.com/cacticloud/cactikit/tools/pretty"
	"github.com/google/uuid"
	"github.com/santhosh-tekuri/jsonschema/v6"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignMutationHandler struct {
	logger     *slog.Logger
	schema     *jsonschema.Schema
	controller *controllers.DesignMutationController
}

func NewDesignMutationHandler(logger *slog.Logger, schema *jsonschema.Schema, c *controllers.DesignMutationController) *DesignMutationHandler {
	if schema == nil {
		panic("schema cannot be nil")
	}
	if c == nil {
		panic("controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignMutationHandler{schema: schema, logger: logger, controller: c}
}

func (h *DesignMutationHandler) parseDesign(r *http.Request, validate bool) (adapters.Design, error) {
	var design adapters.Design
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return design, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return design, fmt.Errorf("empty request body")
	}
	var payload map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return design, fmt.Errorf("invalid JSON payload: %w", err)
	}
	if validate {
		if err := h.schema.Validate(payload); err != nil {
			h.logger.ErrorContext(r.Context(), "Invalid design", slog.String("design", fmt.Sprintf("%#v", payload)))
			return design, err
		}
	}
	if err := json.Unmarshal(body, &design); err != nil {
		return design, err
	}
	return design, nil
}

func (h *DesignMutationHandler) HandlePost(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling POST request...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	design, err := h.parseDesign(r, true)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing design payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if design.ID != "" {
		h.logger.ErrorContext(ctx, "Attempt to create design with specified ID")
	}
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.controller.CreateDesign(ctx, projectId, design, presenter)
}

func (h *DesignMutationHandler) HandlePatch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PATCH request...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	design, err := h.parseDesign(r, false)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error parsing payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if err := design.AlignId(r.PathValue("designId")); err != nil {
		http.Error(w, "Design ID in payload does not match URL", http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	fmt.Println("Incoming fields: ", pretty.ToJSON(design))
	h.controller.ModifyDesign(ctx, projectId, design, presenter)
}

func (h *DesignMutationHandler) HandlePut(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PUT request...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	design, err := h.parseDesign(r, true)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing design payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if err := design.AlignId(r.PathValue("designId")); err != nil {
		http.Error(w, "Design ID in payload does not match URL", http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.controller.SaveDesign(ctx, projectId, design, presenter)
}

func (h *DesignMutationHandler) HandlePutAllDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.logger.InfoContext(ctx, "Handling PUT request for all designs in project...", slog.String("projectId", projectId.String()))
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}
	var payload []map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	designs := []adapters.Design{}
	err = json.Unmarshal(body, &designs)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing designs payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	errors := []error{}
	validDesigns := []adapters.Design{}
	for i, d := range payload {
		if err := h.schema.Validate(d); err != nil {
			h.logger.ErrorContext(r.Context(), "Invalid design",
				slog.String("error", err.Error()), slog.String("design", fmt.Sprintf("%#v", payload)))
			errors = append(errors, err)
			continue
		}
		validDesigns = append(validDesigns, designs[i])
	}
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	if len(errors) > 0 {
		h.logger.InfoContext(ctx, "Some designs are invalid", slog.Int("count", len(errors)))
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	h.controller.SaveAllDesignsForProject(ctx, projectId, validDesigns, presenter)
}

func (h *DesignMutationHandler) HandleDelete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling DELETE request...")
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	h.controller.DeleteDesign(ctx, projectId, designUUID, presenter)
}
