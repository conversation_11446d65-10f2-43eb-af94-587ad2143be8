package gateways_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

const projId = "PRJ-FOOBAR"

func TestFakeDb(t *testing.T) {
	r := gateways.NewFakeRelDb()
	design := usecases.Design{
		ID:        uuid.New(),
		ProjectID: projId,
	}
	id, err := r.UpsertDesign(context.Background(), design)
	require.NoError(t, err)
	require.Equal(t, design.ID, id)
	design, err = r.ReadDesign(context.Background(), id)
	require.NoError(t, err)
	require.Equal(t, id, design.ID)
	designs, err := r.DesignsForProject(context.Background(), projId)
	require.NoError(t, err)
	require.Len(t, designs, 1)
	require.Equal(t, id, designs[0].ID)
	err = r.DeleteDesign(context.Background(), id)
	require.NoError(t, err)
	_, err = r.ReadDesign(context.Background(), id)
	require.ErrorIs(t, err, usecases.ErrNotFound)
	designs, err = r.DesignsForProject(context.Background(), projId)
	require.Len(t, designs, 0)
}
