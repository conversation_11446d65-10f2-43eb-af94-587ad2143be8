DROP SCHEMA IF EXISTS design CASCADE;

CREATE SCHEMA design;

SET
    search_path TO public,
    design;

CREATE TYPE design.color_scheme_enum AS ENUM ('Neutral', 'Bold');

CREATE TYPE design.style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);

CREATE TYPE design.tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);

CREATE TYPE design.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Archived',
    'Outdated'
);

CREATE TABLE design.room_designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    project_id TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status TEXT,
    title TEXT,
    description TEXT,
    color_scheme color_scheme_enum,
    style style_enum
);

CREATE TABLE design.default_products (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    floor_tile UUID,
    floor_tile_pattern tile_pattern_enum,
    paint UUID,
    toilet UUID,
    vanity UUID,
    faucet UUID,
    mirror UUID,
    lighting UUID,
    shelving UUID,
    wall_tile_placement TEXT NOT NULL,
    wall_tile UUID,
    wall_tile_pattern tile_pattern_enum,
    wallpaper_placement TEXT NOT NULL,
    wallpaper UUID,
    shower_system UUID,
    shower_floor_tile UUID,
    shower_floor_tile_pattern tile_pattern_enum,
    shower_wall_tile UUID,
    shower_wall_tile_pattern tile_pattern_enum,
    shower_short_wall_tile UUID,
    shower_glass UUID,
    niche_tile UUID,
    tub UUID,
    tub_filler UUID,
    tub_door UUID
);

CREATE TABLE design.render_prefs (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    shower_glass_visible BOOLEAN NOT NULL DEFAULT FALSE,
    tub_door_visible BOOLEAN NOT NULL DEFAULT FALSE,
    niches_visible BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE design.retail_info (
    room_design_id UUID PRIMARY KEY REFERENCES room_designs (id) ON DELETE CASCADE,
    total_price_cents INTEGER,
    lead_time_days INTEGER,
    sku_count INTEGER
);

CREATE TABLE design.renditions (
    id UUID PRIMARY KEY,
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status rendition_status_enum NOT NULL,
    url TEXT,
    CONSTRAINT chk_rendition_url CHECK (
        status <> 'Completed'
        OR url IS NOT NULL
    )
);