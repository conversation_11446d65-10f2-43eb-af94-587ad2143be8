package gateways

import (
	"database/sql"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// RoomDesignModel corresponds to the 'room_designs' table.
type RoomDesignModel struct {
	ID          uuid.UUID
	ProjectID   string
	CreatedAt   time.Time
	UpdatedAt   time.Time
	Status      sql.NullString
	Style       sql.NullString
	ColorScheme sql.NullString
	Title       sql.NullString
	Description sql.NullString
}

// DefaultProductsModel corresponds to the 'default_products' table.
type DefaultProductsModel struct {
	RoomDesignID           uuid.UUID
	FloorTile              *uuid.UUID
	FloorTilePattern       *usecases.TilePattern
	Toilet                 *uuid.UUID
	Vanity                 *uuid.UUID
	Faucet                 *uuid.UUID
	Mirror                 *uuid.UUID
	Lighting               *uuid.UUID
	Paint                  *uuid.UUID
	Shelving               *uuid.UUID
	WallTilePlacement      usecases.WallTilePlacement
	WallTile               *uuid.UUID
	WallTilePattern        *usecases.TilePattern
	WallpaperPlacement     usecases.WallpaperPlacement
	Wallpaper              *uuid.UUID
	ShowerSystem           *uuid.UUID
	ShowerFloorTile        *uuid.UUID
	ShowerFloorTilePattern *usecases.TilePattern
	ShowerWallTile         *uuid.UUID
	ShowerWallTilePattern  *usecases.TilePattern
	ShowerShortWallTile    *uuid.UUID
	ShowerGlass            *uuid.UUID
	NicheTile              *uuid.UUID
	Tub                    *uuid.UUID
	TubFiller              *uuid.UUID
	TubDoor                *uuid.UUID
}

// RenderPrefsModel corresponds to the 'render_prefs' table.
type RenderPrefsModel struct {
	RoomDesignID       uuid.UUID
	ShowerGlassVisible bool
	TubDoorVisible     bool
	NichesVisible      bool
}
