package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

// Provides access to the Postgres DB used by the monolith.
type Postgres struct {
	dbpool *pgxpool.Pool
	logger *slog.Logger
}

func NewPostgres(dbpool *pgxpool.Pool, logger *slog.Logger) *Postgres {
	if dbpool == nil {
		panic("dbpool cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &Postgres{dbpool: dbpool, logger: logger}
}

func (p *Postgres) Close() {
	p.dbpool.Close()
}

func (p *Postgres) CountProjectsWithDesigns(ctx context.Context) (int, error) {
	var count int
	row := p.dbpool.QueryRow(ctx, "SELECT COUNT(designs) FROM projects")
	if err := row.Scan(&count); err != nil {
		return 0, fmt.Errorf("scan failed: %w", err)
	}
	return count, nil
}

func (p *Postgres) IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error) {
	projectIds := make([]entities.ProjectId, 0)
	rows, err := p.dbpool.Query(ctx, "SELECT id FROM projects WHERE designs IS NOT NULL")
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()
	for rows.Next() {
		var projectId entities.ProjectId
		if err := rows.Scan(&projectId); err != nil {
			return nil, fmt.Errorf("scan failed: %w", err)
		}
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

func (p *Postgres) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	var designs []adapters.Design
	row := p.dbpool.QueryRow(ctx, "SELECT designs FROM projects WHERE id = $1", projectId)
	var data []byte
	if err := row.Scan(&data); err != nil {
		return nil, fmt.Errorf("reading designs for project %s failed: %w", projectId, err)
	}
	if len(data) == 0 {
		return []adapters.Design{}, nil
	}
	if err := json.Unmarshal(data, &designs); err != nil {
		return nil, fmt.Errorf("unmarshal failed: %w", err)
	}
	return designs, nil
}

func (p *Postgres) DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (
	map[entities.ProjectId][]adapters.Design, []error, error) {
	// TODO: Optimize this to a single query with an IN clause.
	results := make(map[entities.ProjectId][]adapters.Design)
	errors := []error{}
	for _, projectId := range projectIds {
		designs, err := p.DesignsForProject(ctx, projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		p.logger.DebugContext(ctx, "Found designs for project", slog.Int("count", len(designs)), slog.String("projectId", projectId.String()))
		results[projectId] = designs
	}
	return results, errors, nil
}

func (p *Postgres) UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error {
	data, err := json.Marshal(designs)
	if err != nil {
		return fmt.Errorf("marshal failed: %w", err)
	}
	_, err = p.dbpool.Exec(ctx, "UPDATE projects SET designs = $1 WHERE id = $2", data, projectId)
	if err != nil {
		return fmt.Errorf("updating designs for project %s failed: %w", projectId, err)
	}
	return nil
}

func (p *Postgres) MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error {
	_, err := p.dbpool.Exec(ctx, "UPDATE renders SET status = 'outdated' WHERE design_id = $1", designId)
	if err != nil {
		return fmt.Errorf("updating render status for design %s failed: %w", designId, err)
	}
	return nil
}
