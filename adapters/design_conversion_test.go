package adapters_test

import (
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

var defaultFloorTilePattern usecases.TilePattern = usecases.HorizontalStacked

func TestMinimalRoundTripConversion(t *testing.T) {
	design := adapters.Design{ID: uuid.NewString()}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)

	// Account for expected diffs:
	design.WallTilePlacement = roundTripped.WallTilePlacement
	design.WallpaperPlacement = roundTripped.WallpaperPlacement
	nope := false
	design.IsShowerGlassVisible = &nope
	design.IsTubDoorVisible = &nope
	design.IsNichesVisible = &nope
	design.FloorTilePattern = &defaultFloorTilePattern

	assert.Equal(t, design, roundTripped)
}

func TestTrivialRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	vanityWall := usecases.VanityWall
	none := usecases.NoWallTile
	nope := false
	design := adapters.Design{
		ID:                   uuid.NewString(),
		Tags:                 0,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &vanityWall,
		WallTilePlacement:    &none,
		FloorTilePattern:     &defaultFloorTilePattern,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, roundTripped)
}

func TestTypicalRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	title := "Test Design"
	description := "This is a test design"
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lighting := uuid.NewString()
	paint := uuid.NewString()
	shelves := uuid.NewString()
	showerSystem := uuid.NewString()
	showerWallTile := uuid.NewString()
	showerWallTilePattern := usecases.Herringbone
	tub := uuid.NewString()
	wallpaper := uuid.NewString()
	wallTile := uuid.NewString()
	wallTilePattern := usecases.Herringbone
	showerFloorTile := uuid.NewString()
	showerFloorTilePattern := usecases.Herringbone
	tubDoor := uuid.NewString()
	halfWall := usecases.HalfWall
	vanityWall := usecases.VanityWall
	nope := false
	design := adapters.Design{
		ID:                     uuid.NewString(),
		Tags:                   36,
		LastUpdatedDateTime:    &lastUpdated,
		Title:                  &title,
		Description:            &description,
		FloorTile:              &floorTile,
		FloorTilePattern:       &defaultFloorTilePattern,
		Toilet:                 &toilet,
		Vanity:                 &vanity,
		Faucet:                 &faucet,
		Mirror:                 &mirror,
		Lighting:               &lighting,
		Paint:                  &paint,
		Shelves:                &shelves,
		ShowerFloorTile:        &showerFloorTile,
		ShowerFloorTilePattern: &showerFloorTilePattern,
		ShowerSystem:           &showerSystem,
		ShowerWallTile:         &showerWallTile,
		ShowerWallTilePattern:  &showerWallTilePattern,
		Tub:                    &tub,
		TubDoor:                &tubDoor,
		WallpaperPlacement:     &vanityWall,
		Wallpaper:              &wallpaper,
		WallTilePlacement:      &halfWall,
		WallTile:               &wallTile,
		WallTilePattern:        &wallTilePattern,
		IsShowerGlassVisible:   &nope,
		IsTubDoorVisible:       &nope,
		IsNichesVisible:        &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, roundTripped)
}

func TestMaximalRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	title := "Test Design"
	description := "This is a test design"
	floorTile := uuid.NewString()
	floorTilePattern := usecases.Herringbone
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lighting := uuid.NewString()
	paint := uuid.NewString()
	shelves := uuid.NewString()
	showerSystem := uuid.NewString()
	showerWallTile := uuid.NewString()
	showerWallTilePattern := usecases.Herringbone
	showerShortWallTile := uuid.NewString()
	showerGlass := uuid.NewString()
	tub := uuid.NewString()
	tubFiller := uuid.NewString()
	wallpaper := uuid.NewString()
	wallTile := uuid.NewString()
	wallTilePattern := usecases.Herringbone
	showerFloorTile := uuid.NewString()
	showerFloorTilePattern := usecases.Herringbone
	nicheTile := uuid.NewString()
	halfWall := usecases.HalfWall
	vanityWall := usecases.VanityWall
	nope := false
	design := adapters.Design{
		ID:                     uuid.NewString(),
		Tags:                   40,
		LastUpdatedDateTime:    &lastUpdated,
		Title:                  &title,
		Description:            &description,
		FloorTile:              &floorTile,
		FloorTilePattern:       &floorTilePattern,
		Toilet:                 &toilet,
		Vanity:                 &vanity,
		Faucet:                 &faucet,
		Mirror:                 &mirror,
		Lighting:               &lighting,
		NicheTile:              &nicheTile,
		Paint:                  &paint,
		Shelves:                &shelves,
		ShowerFloorTile:        &showerFloorTile,
		ShowerFloorTilePattern: &showerFloorTilePattern,
		ShowerSystem:           &showerSystem,
		ShowerWallTile:         &showerWallTile,
		ShowerWallTilePattern:  &showerWallTilePattern,
		ShowerShortWallTile:    &showerShortWallTile,
		ShowerGlass:            &showerGlass,
		Tub:                    &tub,
		TubFiller:              &tubFiller,
		TubDoor:                &tub,
		WallpaperPlacement:     &vanityWall,
		Wallpaper:              &wallpaper,
		WallTilePlacement:      &halfWall,
		WallTile:               &wallTile,
		WallTilePattern:        &wallTilePattern,
		IsShowerGlassVisible:   &nope,
		IsTubDoorVisible:       &nope,
		IsNichesVisible:        &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, roundTripped)
}

// Test individual conversion helper functions
func TestTagsFromDomain(t *testing.T) {
	tests := []struct {
		name     string
		design   usecases.Design
		expected int64
	}{
		{
			name: "no status, no color scheme, no style",
			design: usecases.Design{
				Status: "",
			},
			expected: 0,
		},
		{
			name: "fave status only",
			design: usecases.Design{
				Status: usecases.Fave,
			},
			expected: 1073741824, // 2^30
		},
		{
			name: "archived status only",
			design: usecases.Design{
				Status: usecases.Archived,
			},
			expected: 536870912, // 2^29
		},
		{
			name: "neutral color scheme only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
				},
			},
			expected: 32, // 2^5
		},
		{
			name: "bold color scheme only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					ColorScheme: &[]usecases.ColorScheme{usecases.Bold}[0],
				},
			},
			expected: 16, // 2^4
		},
		{
			name: "mid-century style only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					Style: &[]usecases.Style{usecases.MidCentury}[0],
				},
			},
			expected: 1, // 2^0
		},
		{
			name: "traditional style only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					Style: &[]usecases.Style{usecases.Traditional}[0],
				},
			},
			expected: 2, // 2^1
		},
		{
			name: "modern style only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					Style: &[]usecases.Style{usecases.Modern}[0],
				},
			},
			expected: 4, // 2^2
		},
		{
			name: "transitional style only",
			design: usecases.Design{
				DesignOptions: usecases.DesignOptions{
					Style: &[]usecases.Style{usecases.Transitional}[0],
				},
			},
			expected: 8, // 2^3
		},
		{
			name: "combination: fave + bold + modern",
			design: usecases.Design{
				Status: usecases.Fave,
				DesignOptions: usecases.DesignOptions{
					ColorScheme: &[]usecases.ColorScheme{usecases.Bold}[0],
					Style:       &[]usecases.Style{usecases.Modern}[0],
				},
			},
			expected: 1073741824 + 16 + 4, // 2^30 + 2^4 + 2^2
		},
		{
			name: "combination: archived + neutral + traditional",
			design: usecases.Design{
				Status: usecases.Archived,
				DesignOptions: usecases.DesignOptions{
					ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
					Style:       &[]usecases.Style{usecases.Traditional}[0],
				},
			},
			expected: 536870912 + 32 + 2, // 2^29 + 2^5 + 2^1
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// We need to access the private function through the public API
			// by creating a design and converting it
			apiDesign := adapters.FromUsecaseDesign(tt.design)
			assert.Equal(t, tt.expected, apiDesign.Tags)
		})
	}
}

func TestStatusFromTags(t *testing.T) {
	tests := []struct {
		name     string
		tags     int64
		expected usecases.DesignStatus
	}{
		{
			name:     "no status bits set",
			tags:     0,
			expected: "",
		},
		{
			name:     "fave status bit set",
			tags:     1073741824, // 2^30
			expected: usecases.Fave,
		},
		{
			name:     "archived status bit set",
			tags:     536870912, // 2^29
			expected: usecases.Archived,
		},
		{
			name:     "both status bits set (fave takes precedence)",
			tags:     1073741824 + 536870912, // 2^30 + 2^29
			expected: usecases.Fave,
		},
		{
			name:     "fave with other bits",
			tags:     1073741824 + 32 + 4, // 2^30 + 2^5 + 2^2
			expected: usecases.Fave,
		},
		{
			name:     "archived with other bits",
			tags:     536870912 + 16 + 2, // 2^29 + 2^4 + 2^1
			expected: usecases.Archived,
		},
		{
			name:     "only other bits set",
			tags:     32 + 16 + 8 + 4 + 2 + 1, // all style and color scheme bits
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test through the public API by creating a design with tags and converting
			apiDesign := adapters.Design{
				ID:   uuid.NewString(),
				Tags: tt.tags,
			}
			ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
			require.NoError(t, err)
			assert.Equal(t, tt.expected, ucDesign.Status)
		})
	}
}

func TestColorSchemeFromTags(t *testing.T) {
	tests := []struct {
		name     string
		tags     int64
		expected *usecases.ColorScheme
	}{
		{
			name:     "no color scheme bits set",
			tags:     0,
			expected: nil,
		},
		{
			name:     "neutral color scheme bit set",
			tags:     32, // 2^5
			expected: &[]usecases.ColorScheme{usecases.Neutral}[0],
		},
		{
			name:     "bold color scheme bit set",
			tags:     16, // 2^4
			expected: &[]usecases.ColorScheme{usecases.Bold}[0],
		},
		{
			name:     "both color scheme bits set (neutral takes precedence)",
			tags:     32 + 16, // 2^5 + 2^4
			expected: &[]usecases.ColorScheme{usecases.Neutral}[0],
		},
		{
			name:     "neutral with other bits",
			tags:     32 + 1073741824 + 4, // 2^5 + 2^30 + 2^2
			expected: &[]usecases.ColorScheme{usecases.Neutral}[0],
		},
		{
			name:     "bold with other bits",
			tags:     16 + 536870912 + 2, // 2^4 + 2^29 + 2^1
			expected: &[]usecases.ColorScheme{usecases.Bold}[0],
		},
		{
			name:     "only other bits set",
			tags:     1073741824 + 536870912 + 8 + 4 + 2 + 1, // status and style bits
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test through the public API by creating a design with tags and converting
			apiDesign := adapters.Design{
				ID:   uuid.NewString(),
				Tags: tt.tags,
			}
			ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
			require.NoError(t, err)
			if tt.expected == nil {
				assert.Nil(t, ucDesign.ColorScheme)
			} else {
				require.NotNil(t, ucDesign.ColorScheme)
				assert.Equal(t, *tt.expected, *ucDesign.ColorScheme)
			}
		})
	}
}

func TestStyleFromTags(t *testing.T) {
	tests := []struct {
		name     string
		tags     int64
		expected *usecases.Style
	}{
		{
			name:     "no style bits set",
			tags:     0,
			expected: nil,
		},
		{
			name:     "mid-century style bit set",
			tags:     1, // 2^0
			expected: &[]usecases.Style{usecases.MidCentury}[0],
		},
		{
			name:     "traditional style bit set",
			tags:     2, // 2^1
			expected: &[]usecases.Style{usecases.Traditional}[0],
		},
		{
			name:     "modern style bit set",
			tags:     4, // 2^2
			expected: &[]usecases.Style{usecases.Modern}[0],
		},
		{
			name:     "transitional style bit set",
			tags:     8, // 2^3
			expected: &[]usecases.Style{usecases.Transitional}[0],
		},
		{
			name:     "multiple style bits set (mid-century takes precedence)",
			tags:     1 + 2 + 4 + 8, // all style bits
			expected: &[]usecases.Style{usecases.MidCentury}[0],
		},
		{
			name:     "traditional and modern set (traditional takes precedence)",
			tags:     2 + 4, // 2^1 + 2^2
			expected: &[]usecases.Style{usecases.Traditional}[0],
		},
		{
			name:     "modern and transitional set (modern takes precedence)",
			tags:     4 + 8, // 2^2 + 2^3
			expected: &[]usecases.Style{usecases.Modern}[0],
		},
		{
			name:     "style with other bits",
			tags:     4 + 1073741824 + 32, // modern + fave + neutral
			expected: &[]usecases.Style{usecases.Modern}[0],
		},
		{
			name:     "only other bits set",
			tags:     1073741824 + 536870912 + 32 + 16, // status and color scheme bits
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test through the public API by creating a design with tags and converting
			apiDesign := adapters.Design{
				ID:   uuid.NewString(),
				Tags: tt.tags,
			}
			ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
			require.NoError(t, err)
			if tt.expected == nil {
				assert.Nil(t, ucDesign.Style)
			} else {
				require.NotNil(t, ucDesign.Style)
				assert.Equal(t, *tt.expected, *ucDesign.Style)
			}
		})
	}
}

func TestUUIDConversions(t *testing.T) {
	t.Run("uuidPtrToStringPtr", func(t *testing.T) {
		// Test through the public API by creating a usecase design with UUID and converting
		testUUID := uuid.New()
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				FloorTile: &testUUID,
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.FloorTile)
		assert.Equal(t, testUUID.String(), *apiDesign.FloorTile)
	})

	t.Run("uuidPtrToStringPtr with nil", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				FloorTile: nil,
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.FloorTile)
	})

	t.Run("stringPtrToUUIDPtr valid UUID", func(t *testing.T) {
		testUUID := uuid.New()
		testUUIDStr := testUUID.String()

		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: &testUUIDStr,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		require.NotNil(t, ucDesign.FloorTile)
		assert.Equal(t, testUUID, *ucDesign.FloorTile)
	})

	t.Run("stringPtrToUUIDPtr with nil", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Nil(t, ucDesign.FloorTile)
	})

	t.Run("stringPtrToUUIDPtr with empty string", func(t *testing.T) {
		emptyStr := ""
		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: &emptyStr,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Nil(t, ucDesign.FloorTile)
	})

	t.Run("stringPtrToUUIDPtr with invalid UUID", func(t *testing.T) {
		invalidUUID := "not-a-uuid"
		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: &invalidUUID,
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid UUID for FloorTile")
	})
}

func TestSQLNullConversions(t *testing.T) {
	t.Run("sqlNullStringToStringPtr valid", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				Title: sql.NullString{String: "Test Title", Valid: true},
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.Title)
		assert.Equal(t, "Test Title", *apiDesign.Title)
	})

	t.Run("sqlNullStringToStringPtr invalid", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				Title: sql.NullString{String: "", Valid: false},
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.Title)
	})

	t.Run("stringPtrToSQLNullString valid", func(t *testing.T) {
		title := "Test Title"
		apiDesign := adapters.Design{
			ID:    uuid.NewString(),
			Title: &title,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.True(t, ucDesign.Title.Valid)
		assert.Equal(t, "Test Title", ucDesign.Title.String)
	})

	t.Run("stringPtrToSQLNullString nil", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:    uuid.NewString(),
			Title: nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.False(t, ucDesign.Title.Valid)
		assert.Equal(t, "", ucDesign.Title.String)
	})

	t.Run("sqlNullInt32ToInt32Ptr valid", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				NumSKUs: sql.NullInt32{Int32: 42, Valid: true},
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.SKUCount)
		assert.Equal(t, int32(42), *apiDesign.SKUCount)
	})

	t.Run("sqlNullInt32ToInt32Ptr invalid", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				NumSKUs: sql.NullInt32{Int32: 0, Valid: false},
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.SKUCount)
	})

	t.Run("int32PtrToSQLNullInt32 valid", func(t *testing.T) {
		skuCount := int32(42)
		apiDesign := adapters.Design{
			ID:       uuid.NewString(),
			SKUCount: &skuCount,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.True(t, ucDesign.NumSKUs.Valid)
		assert.Equal(t, int32(42), ucDesign.NumSKUs.Int32)
	})

	t.Run("int32PtrToSQLNullInt32 nil", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:       uuid.NewString(),
			SKUCount: nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.False(t, ucDesign.NumSKUs.Valid)
		assert.Equal(t, int32(0), ucDesign.NumSKUs.Int32)
	})
}

func TestEnumConversions(t *testing.T) {
	t.Run("enumToEnumPtr with non-zero value", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PROJ-1234",
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.WallpaperPlacement)
		assert.Equal(t, usecases.VanityWall, *apiDesign.WallpaperPlacement)
		require.NotNil(t, apiDesign.WallTilePlacement)
		assert.Equal(t, usecases.HalfWall, *apiDesign.WallTilePlacement)
	})

	t.Run("enumToEnumPtr with zero value", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PROJ-1234",
			WallpaperPlacement: "",
			WallTilePlacement:  "",
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.WallpaperPlacement)
		assert.Nil(t, apiDesign.WallTilePlacement)
	})

	t.Run("enum pointer to enum conversion", func(t *testing.T) {
		vanityWall := usecases.VanityWall
		halfWall := usecases.HalfWall

		apiDesign := adapters.Design{
			ID:                 uuid.NewString(),
			WallpaperPlacement: &vanityWall,
			WallTilePlacement:  &halfWall,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Equal(t, usecases.VanityWall, ucDesign.WallpaperPlacement)
		assert.Equal(t, usecases.HalfWall, ucDesign.WallTilePlacement)
	})

	t.Run("nil enum pointer to default enum conversion", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:                 uuid.NewString(),
			WallpaperPlacement: nil,
			WallTilePlacement:  nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		// When WallpaperPlacement is nil, it defaults to VanityWall
		assert.Equal(t, usecases.VanityWall, ucDesign.WallpaperPlacement)
		// When WallTilePlacement is nil and no WallTile is set, it defaults to NoWallTile
		assert.Equal(t, usecases.NoWallTile, ucDesign.WallTilePlacement)
	})

	t.Run("nil wall tile placement with wall tile set", func(t *testing.T) {
		wallTileID := uuid.NewString()
		apiDesign := adapters.Design{
			ID:                uuid.NewString(),
			WallTile:          &wallTileID,
			WallTilePlacement: nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		// When WallTilePlacement is nil but WallTile is set, it defaults to HalfWall
		assert.Equal(t, usecases.HalfWall, ucDesign.WallTilePlacement)
	})
}

func TestTimeConversions(t *testing.T) {
	t.Run("time to RFC3339 string", func(t *testing.T) {
		testTime := time.Date(2025, 6, 13, 0, 59, 59, 0, time.UTC)
		ucDesign := usecases.Design{
			ID:          uuid.New(),
			ProjectID:   "PROJ-1234",
			LastUpdated: testTime,
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.LastUpdatedDateTime)
		assert.Equal(t, "2025-06-13T00:59:59Z", *apiDesign.LastUpdatedDateTime)
	})

	t.Run("zero time to nil string", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:          uuid.New(),
			ProjectID:   "PROJ-1234",
			LastUpdated: time.Time{}, // zero time
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.LastUpdatedDateTime)
	})

	t.Run("RFC3339 string to time", func(t *testing.T) {
		timeStr := "2025-06-13T00:59:59Z"
		apiDesign := adapters.Design{
			ID:                  uuid.NewString(),
			LastUpdatedDateTime: &timeStr,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		expectedTime := time.Date(2025, 6, 13, 0, 59, 59, 0, time.UTC)
		assert.Equal(t, expectedTime, ucDesign.LastUpdated)
	})

	t.Run("nil time string to zero time", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:                  uuid.NewString(),
			LastUpdatedDateTime: nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.True(t, ucDesign.LastUpdated.IsZero())
	})

	t.Run("invalid time string", func(t *testing.T) {
		invalidTime := "not-a-time"
		apiDesign := adapters.Design{
			ID:                  uuid.NewString(),
			LastUpdatedDateTime: &invalidTime,
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid last updated date time")
	})
}

func TestDesignIDHandling(t *testing.T) {
	t.Run("valid UUID ID", func(t *testing.T) {
		validUUID := uuid.NewString()
		apiDesign := adapters.Design{
			ID: validUUID,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Equal(t, validUUID, ucDesign.ID.String())
		assert.NotEqual(t, usecases.Preview, ucDesign.Status)
	})

	t.Run("two character ID generates new UUID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:   "AB", // Two character ID
			Tags: 0,    // No status bits set
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.NotEqual(t, "AB", ucDesign.ID.String()) // Should be a new UUID
		assert.Equal(t, usecases.Preview, ucDesign.Status)
		// Verify it's a valid UUID
		_, err = uuid.Parse(ucDesign.ID.String())
		assert.NoError(t, err)
	})

	t.Run("invalid UUID ID (not 2 chars)", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "invalid-uuid-string",
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})

	t.Run("empty ID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "",
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})

	t.Run("single character ID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "A",
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})

	t.Run("three character ID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "ABC",
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})
}

func TestToUsecaseDesignDiff(t *testing.T) {
	t.Run("valid design diff conversion", func(t *testing.T) {
		validUUID := uuid.NewString()
		title := "Test Title"
		vanityWall := usecases.VanityWall

		apiDesign := adapters.Design{
			ID:                 validUUID,
			Tags:               1073741824 + 32 + 4, // Fave + Neutral + Modern
			Title:              &title,
			WallpaperPlacement: &vanityWall,
		}

		designDiff, err := apiDesign.ToUsecaseDesignDiff()
		require.NoError(t, err)

		assert.Equal(t, validUUID, designDiff.ID.String())
		require.NotNil(t, designDiff.Status)
		assert.Equal(t, usecases.Fave, *designDiff.Status)
		require.NotNil(t, designDiff.ColorScheme)
		assert.Equal(t, usecases.Neutral, *designDiff.ColorScheme)
		require.NotNil(t, designDiff.Style)
		assert.Equal(t, usecases.Modern, *designDiff.Style)
		require.NotNil(t, designDiff.WallpaperPlacement)
		assert.Equal(t, usecases.VanityWall, *designDiff.WallpaperPlacement)
		assert.True(t, designDiff.Title.Valid)
		assert.Equal(t, "Test Title", designDiff.Title.String)
	})

	t.Run("design diff with invalid UUID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "invalid-uuid",
		}

		_, err := apiDesign.ToUsecaseDesignDiff()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})

	t.Run("design diff with no tags", func(t *testing.T) {
		validUUID := uuid.NewString()

		apiDesign := adapters.Design{
			ID:   validUUID,
			Tags: 0,
		}

		designDiff, err := apiDesign.ToUsecaseDesignDiff()
		require.NoError(t, err)

		assert.Equal(t, validUUID, designDiff.ID.String())
		assert.Nil(t, designDiff.Status)
		assert.Nil(t, designDiff.ColorScheme)
		assert.Nil(t, designDiff.Style)
	})
}

func TestBooleanConversions(t *testing.T) {
	t.Run("boolean to pointer conversion", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PROJ-1234",
			ShowerGlassVisible: true,
			TubDoorVisible:     false,
			NichesVisible:      true,
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		require.NotNil(t, apiDesign.IsShowerGlassVisible)
		assert.True(t, *apiDesign.IsShowerGlassVisible)
		require.NotNil(t, apiDesign.IsTubDoorVisible)
		assert.False(t, *apiDesign.IsTubDoorVisible)
		require.NotNil(t, apiDesign.IsNichesVisible)
		assert.True(t, *apiDesign.IsNichesVisible)
	})

	t.Run("pointer to boolean conversion", func(t *testing.T) {
		showerGlass := true
		tubDoor := false
		niches := true

		apiDesign := adapters.Design{
			ID:                   uuid.NewString(),
			IsShowerGlassVisible: &showerGlass,
			IsTubDoorVisible:     &tubDoor,
			IsNichesVisible:      &niches,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.True(t, ucDesign.ShowerGlassVisible)
		assert.False(t, ucDesign.TubDoorVisible)
		assert.True(t, ucDesign.NichesVisible)
	})

	t.Run("nil pointer to false boolean conversion", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID:                   uuid.NewString(),
			IsShowerGlassVisible: nil,
			IsTubDoorVisible:     nil,
			IsNichesVisible:      nil,
		}

		ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.False(t, ucDesign.ShowerGlassVisible)
		assert.False(t, ucDesign.TubDoorVisible)
		assert.False(t, ucDesign.NichesVisible)
	})
}

func TestTilePatternConversions(t *testing.T) {
	t.Run("valid tile patterns", func(t *testing.T) {
		patterns := []usecases.TilePattern{
			usecases.HorizontalStacked,
			usecases.VerticalStacked,
			usecases.HalfOffset,
			usecases.ThirdOffset,
			usecases.Herringbone,
		}

		for _, pattern := range patterns {
			t.Run(string(pattern), func(t *testing.T) {
				ucDesign := usecases.Design{
					ID:        uuid.New(),
					ProjectID: "PROJ-1234",
					DesignOptions: usecases.DesignOptions{
						FloorTilePattern: &pattern,
					},
				}

				apiDesign := adapters.FromUsecaseDesign(ucDesign)
				require.NotNil(t, apiDesign.FloorTilePattern)
				assert.Equal(t, pattern, *apiDesign.FloorTilePattern)

				// Test round trip
				ucDesignRoundTrip, err := apiDesign.ToUsecaseDesign("PROJ-1234")
				require.NoError(t, err)
				require.NotNil(t, ucDesignRoundTrip.FloorTilePattern)
				assert.Equal(t, pattern, *ucDesignRoundTrip.FloorTilePattern)
			})
		}
	})

	t.Run("nil tile pattern", func(t *testing.T) {
		ucDesign := usecases.Design{
			ID:        uuid.New(),
			ProjectID: "PROJ-1234",
			DesignOptions: usecases.DesignOptions{
				FloorTilePattern: nil,
			},
		}

		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		assert.Nil(t, apiDesign.FloorTilePattern)

		// Test round trip - when FloorTilePattern is nil, it gets a default value
		ucDesignRoundTrip, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		require.NotNil(t, ucDesignRoundTrip.FloorTilePattern)
		assert.Equal(t, usecases.HorizontalStacked, *ucDesignRoundTrip.FloorTilePattern)
	})
}

func TestErrorCases(t *testing.T) {
	t.Run("multiple invalid UUIDs in single conversion", func(t *testing.T) {
		invalidUUID1 := "invalid-uuid-1"
		invalidUUID2 := "invalid-uuid-2"

		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: &invalidUUID1,
			Toilet:    &invalidUUID2,
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		// Should report the first invalid UUID encountered
		assert.Contains(t, err.Error(), "invalid UUID for")
	})

	t.Run("mixed valid and invalid UUIDs", func(t *testing.T) {
		validUUID := uuid.NewString()
		invalidUUID := "invalid-uuid"

		apiDesign := adapters.Design{
			ID:        uuid.NewString(),
			FloorTile: &validUUID,
			Toilet:    &invalidUUID,
		}

		_, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid UUID for Toilet")
	})

	t.Run("all enum combinations", func(t *testing.T) {
		// Test all combinations of wallpaper and wall tile placements
		wallpaperPlacements := []usecases.WallpaperPlacement{
			usecases.NoWallpaper,
			usecases.AllWalls,
			usecases.VanityWall,
		}

		wallTilePlacements := []usecases.WallTilePlacement{
			usecases.NoWallTile,
			usecases.FullWall,
			usecases.HalfWall,
			usecases.VanityFullWall,
			usecases.VanityHalfWall,
		}

		for _, wp := range wallpaperPlacements {
			for _, wt := range wallTilePlacements {
				t.Run(fmt.Sprintf("wallpaper_%s_walltile_%s", wp, wt), func(t *testing.T) {
					ucDesign := usecases.Design{
						ID:                 uuid.New(),
						ProjectID:          "PROJ-1234",
						WallpaperPlacement: wp,
						WallTilePlacement:  wt,
					}

					apiDesign := adapters.FromUsecaseDesign(ucDesign)
					ucDesignRoundTrip, err := apiDesign.ToUsecaseDesign("PROJ-1234")
					require.NoError(t, err)

					assert.Equal(t, wp, ucDesignRoundTrip.WallpaperPlacement)
					assert.Equal(t, wt, ucDesignRoundTrip.WallTilePlacement)
				})
			}
		}
	})
}

func TestComplexScenarios(t *testing.T) {
	t.Run("design with all fields populated", func(t *testing.T) {
		// Create a design with every possible field set
		testTime := time.Date(2025, 6, 13, 12, 30, 45, 0, time.UTC)

		// Create UUIDs first to get pointers
		faucetID := uuid.New()
		floorTileID := uuid.New()
		lightingID := uuid.New()
		mirrorID := uuid.New()
		paintID := uuid.New()
		shelvingID := uuid.New()
		showerFloorTileID := uuid.New()
		showerSystemID := uuid.New()
		showerWallTileID := uuid.New()
		showerShortWallTileID := uuid.New()
		showerGlassID := uuid.New()
		toiletID := uuid.New()
		tubID := uuid.New()
		tubDoorID := uuid.New()
		tubFillerID := uuid.New()
		vanityID := uuid.New()
		wallpaperID := uuid.New()
		wallTileID := uuid.New()
		nicheTileID := uuid.New()

		ucDesign := usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PROJ-1234",
			Created:            testTime.Add(-time.Hour),
			LastUpdated:        testTime,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				ColorScheme:            &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:                  &[]usecases.Style{usecases.Modern}[0],
				Title:                  sql.NullString{String: "Complex Design", Valid: true},
				Description:            sql.NullString{String: "A very complex design", Valid: true},
				Faucet:                 &faucetID,
				FloorTile:              &floorTileID,
				FloorTilePattern:       &[]usecases.TilePattern{usecases.Herringbone}[0],
				Lighting:               &lightingID,
				Mirror:                 &mirrorID,
				Paint:                  &paintID,
				Shelving:               &shelvingID,
				ShowerFloorTile:        &showerFloorTileID,
				ShowerFloorTilePattern: &[]usecases.TilePattern{usecases.HalfOffset}[0],
				ShowerSystem:           &showerSystemID,
				ShowerWallTile:         &showerWallTileID,
				ShowerWallTilePattern:  &[]usecases.TilePattern{usecases.VerticalStacked}[0],
				ShowerShortWallTile:    &showerShortWallTileID,
				ShowerGlass:            &showerGlassID,
				Toilet:                 &toiletID,
				Tub:                    &tubID,
				TubDoor:                &tubDoorID,
				TubFiller:              &tubFillerID,
				Vanity:                 &vanityID,
				Wallpaper:              &wallpaperID,
				WallTile:               &wallTileID,
				WallTilePattern:        &[]usecases.TilePattern{usecases.ThirdOffset}[0],
				NicheTile:              &nicheTileID,
				NumSKUs:                sql.NullInt32{Int32: 25, Valid: true},
				TotalPriceInCents:      sql.NullInt32{Int32: 150000, Valid: true},
				LeadTimeDays:           sql.NullInt32{Int32: 30, Valid: true},
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     true,
			NichesVisible:      true,
		}

		// Convert to API design and back
		apiDesign := adapters.FromUsecaseDesign(ucDesign)
		ucDesignRoundTrip, err := apiDesign.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)

		// Verify all fields are preserved (except Created which isn't in API design)
		assert.Equal(t, ucDesign.ID, ucDesignRoundTrip.ID)
		assert.Equal(t, ucDesign.ProjectID, ucDesignRoundTrip.ProjectID)
		assert.Equal(t, ucDesign.LastUpdated.Format(time.RFC3339), ucDesignRoundTrip.LastUpdated.Format(time.RFC3339))
		assert.Equal(t, ucDesign.Status, ucDesignRoundTrip.Status)
		assert.Equal(t, ucDesign.WallpaperPlacement, ucDesignRoundTrip.WallpaperPlacement)
		assert.Equal(t, ucDesign.WallTilePlacement, ucDesignRoundTrip.WallTilePlacement)
		assert.Equal(t, ucDesign.ShowerGlassVisible, ucDesignRoundTrip.ShowerGlassVisible)
		assert.Equal(t, ucDesign.TubDoorVisible, ucDesignRoundTrip.TubDoorVisible)
		assert.Equal(t, ucDesign.NichesVisible, ucDesignRoundTrip.NichesVisible)

		// Verify design options
		assert.Equal(t, *ucDesign.ColorScheme, *ucDesignRoundTrip.ColorScheme)
		assert.Equal(t, *ucDesign.Style, *ucDesignRoundTrip.Style)
		assert.Equal(t, ucDesign.Title, ucDesignRoundTrip.Title)
		assert.Equal(t, ucDesign.Description, ucDesignRoundTrip.Description)
		assert.Equal(t, ucDesign.NumSKUs, ucDesignRoundTrip.NumSKUs)
		assert.Equal(t, ucDesign.TotalPriceInCents, ucDesignRoundTrip.TotalPriceInCents)
		assert.Equal(t, ucDesign.LeadTimeDays, ucDesignRoundTrip.LeadTimeDays)
	})
}
