package presenters

import (
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignMutationOutcomePresenter struct {
	w      http.ResponseWriter
	logger *slog.Logger
}

func NewDesignMutationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *DesignMutationOutcomePresenter {
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignMutationOutcomePresenter{logger: logger, w: w}
}

func (p *DesignMutationOutcomePresenter) PresentError(err error) {
	p.logger.Error("Error mutating design", slog.String("error message", err.Error()))
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	switch err {
	case usecases.ErrInvalidPayload:
		http.Error(p.w, err.<PERSON>rror(), http.StatusBadRequest)
		return
	case usecases.ErrNotFound:
		http.Error(p.w, err.Error(), http.StatusNotFound)
		return
	case usecases.ErrConflict:
		http.Error(p.w, "Design with the same ID already exists", http.StatusConflict)
		return
	default:
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
}

func (p *DesignMutationOutcomePresenter) ConveySuccessWithNewResourceLocation(projectId *entities.ProjectId, designId *uuid.UUID) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	if projectId != nil && designId != nil {
		p.w.Header().Set("Location", fmt.Sprintf("/projects/%s/designs/%s", *projectId, *designId))
	}
	p.w.WriteHeader(http.StatusCreated)
	p.w.Write([]byte("{}"))
}

func (p *DesignMutationOutcomePresenter) ConveySuccess() {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	p.w.WriteHeader(http.StatusNoContent)
}
