package presenters_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/cacticloud/cactikit/tools/pretty"
	"github.com/go-json-experiment/json/jsontext"
	"github.com/google/uuid"
	"github.com/kodeart/go-problem/v2"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lastUpdated := "2025-06-13T00:59:59Z"
	none := usecases.NoWallpaper
	halfWall := usecases.HalfWall
	defaultFloorTilePattern := usecases.HorizontalStacked
	nope := false
	return adapters.Design{
		ID:                   uuid.NewString(),
		FloorTile:            &floorTile,
		Toilet:               &toilet,
		Vanity:               &vanity,
		Faucet:               &faucet,
		Mirror:               &mirror,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &none,
		WallTilePlacement:    &halfWall,
		FloorTilePattern:     &defaultFloorTilePattern,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
}

func TestFetchingDesigns(t *testing.T) {
	testDesigns := []adapters.Design{genDesign(), genDesign(), genDesign()}
	data, err := json.Marshal(testDesigns)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesigns)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingSingleDesign(t *testing.T) {
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesign)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingMixedResults(t *testing.T) {
	testDesigns1 := []adapters.Design{genDesign(), genDesign()}
	testDesigns2 := make([]usecases.Design, len(testDesigns1))
	var err error
	for i, d := range testDesigns1 {
		testDesigns2[i], err = d.ToUsecaseDesign("PRJ-FOOBAR")
		require.NoError(t, err)
	}
	designs := map[entities.ProjectId][]usecases.Design{
		"PRJ-FOOBAR": testDesigns2,
	}
	errors := []error{usecases.ErrNotFound}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentDesignsByProject(context.Background(), designs, errors)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
	var errList []*problem.Problem
	for _, err := range errors {
		errList = append(errList, problem.New().WithStatus(http.StatusNotFound).WithDetail(err.Error()))
	}
	results := map[entities.ProjectId]presenters.MultiProjectOutputItem{
		"PRJ-FOOBAR": {
			Status: http.StatusOK,
			Data:   testDesigns1,
		},
		"errors": {
			Status: http.StatusNotFound,
			Errors: errList,
		},
	}
	data, err := json.Marshal(results)
	if err != nil {
		t.Fatal(err)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		(*jsontext.Value)(&data).Indent()
		expected = string(data)
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", pretty.FormatJSON([]byte(body)), expected)
	}
}
