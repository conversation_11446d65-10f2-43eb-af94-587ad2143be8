package presenters_test

import (
	"errors"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func TestNewDesignMutationOutcomePresenter(t *testing.T) {
	t.Run("with logger", func(t *testing.T) {
		logger := slog.Default()
		recorder := httptest.NewRecorder()

		presenter := presenters.NewDesignMutationOutcomePresenter(logger, recorder)

		assert.NotNil(t, presenter)
	})

	t.Run("with nil logger", func(t *testing.T) {
		recorder := httptest.NewRecorder()

		presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)

		assert.NotNil(t, presenter)
	})
}

func TestDesignMutationOutcomePresenter_PresentError(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "ErrInvalidPayload",
			err:            usecases.ErrInvalidPayload,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid payload\n",
		},
		{
			name:           "ErrNotFound",
			err:            usecases.ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "target not found\n",
		},
		{
			name:           "ErrConflict",
			err:            usecases.ErrConflict,
			expectedStatus: http.StatusConflict,
			expectedBody:   "Design with the same ID already exists\n",
		},
		{
			name:           "generic error",
			err:            errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "database connection failed\n",
		},
		{
			name:           "empty error message",
			err:            errors.New(""),
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

			presenter.PresentError(tt.err)

			assert.Equal(t, tt.expectedStatus, recorder.Code)
			assert.Equal(t, tt.expectedBody, recorder.Body.String())
			assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		})
	}
}

func TestDesignMutationOutcomePresenter_PresentError_NilErrorPanics(t *testing.T) {
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

	assert.Panics(t, func() {
		presenter.PresentError(nil)
	}, "PresentError should panic when given a nil error")
}

func TestDesignMutationOutcomePresenter_ConveySuccessWithNewResourceLocation(t *testing.T) {
	t.Run("with valid project ID and design ID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		projectId := entities.ProjectId("PRJ-12345")
		designId := uuid.New()

		presenter.ConveySuccessWithNewResourceLocation(&projectId, &designId)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		assert.Equal(t, "{}", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		expectedLocation := "/projects/PRJ-12345/designs/" + designId.String()
		assert.Equal(t, expectedLocation, recorder.Header().Get("Location"))
	})

	t.Run("with nil project ID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		designId := uuid.New()

		presenter.ConveySuccessWithNewResourceLocation(nil, &designId)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		assert.Equal(t, "{}", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Empty(t, recorder.Header().Get("Location"))
	})

	t.Run("with nil design ID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		projectId := entities.ProjectId("PRJ-12345")

		presenter.ConveySuccessWithNewResourceLocation(&projectId, nil)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		assert.Equal(t, "{}", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Empty(t, recorder.Header().Get("Location"))
	})

	t.Run("with both nil parameters", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		presenter.ConveySuccessWithNewResourceLocation(nil, nil)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		assert.Equal(t, "{}", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Empty(t, recorder.Header().Get("Location"))
	})
}

func TestDesignMutationOutcomePresenter_ConveySuccess(t *testing.T) {
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

	presenter.ConveySuccess()

	assert.Equal(t, http.StatusNoContent, recorder.Code)
	assert.Empty(t, recorder.Body.String())
	assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
}

// Boundary cases and edge conditions

func TestDesignMutationOutcomePresenter_EdgeCases(t *testing.T) {
	t.Run("empty project ID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		projectId := entities.ProjectId("")
		designId := uuid.New()

		presenter.ConveySuccessWithNewResourceLocation(&projectId, &designId)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		expectedLocation := "/projects//designs/" + designId.String()
		assert.Equal(t, expectedLocation, recorder.Header().Get("Location"))
	})

	t.Run("zero UUID", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		projectId := entities.ProjectId("PRJ-12345")
		designId := uuid.UUID{} // zero UUID

		presenter.ConveySuccessWithNewResourceLocation(&projectId, &designId)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		expectedLocation := "/projects/PRJ-12345/designs/00000000-0000-0000-0000-000000000000"
		assert.Equal(t, expectedLocation, recorder.Header().Get("Location"))
	})

	t.Run("project ID with special characters", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), recorder)

		projectId := entities.ProjectId("PRJ-123/456%789")
		designId := uuid.New()

		presenter.ConveySuccessWithNewResourceLocation(&projectId, &designId)

		assert.Equal(t, http.StatusCreated, recorder.Code)
		expectedLocation := "/projects/PRJ-123/456%789/designs/" + designId.String()
		assert.Equal(t, expectedLocation, recorder.Header().Get("Location"))
	})
}

// Mock ResponseWriter that fails on Write
type failingMutationResponseWriter struct {
	*httptest.ResponseRecorder
	shouldFailOnWrite bool
}

func (f *failingMutationResponseWriter) Write(data []byte) (int, error) {
	if f.shouldFailOnWrite {
		return 0, errors.New("write failed")
	}
	return f.ResponseRecorder.Write(data)
}

func TestDesignMutationOutcomePresenter_WriteFailures(t *testing.T) {
	t.Run("write failure on ConveySuccessWithNewResourceLocation", func(t *testing.T) {
		failingWriter := &failingMutationResponseWriter{
			ResponseRecorder:  httptest.NewRecorder(),
			shouldFailOnWrite: true,
		}
		presenter := presenters.NewDesignMutationOutcomePresenter(slog.Default(), failingWriter)

		projectId := entities.ProjectId("PRJ-12345")
		designId := uuid.New()

		// This should not panic even if write fails
		presenter.ConveySuccessWithNewResourceLocation(&projectId, &designId)

		assert.Equal(t, http.StatusCreated, failingWriter.Code)
		assert.Equal(t, "*", failingWriter.Header().Get("Access-Control-Allow-Origin"))
	})
}

// Test with different logger configurations
func TestDesignMutationOutcomePresenter_LoggerVariations(t *testing.T) {
	t.Run("with custom logger", func(t *testing.T) {
		// Create a custom logger that writes to a buffer to verify logging
		recorder := httptest.NewRecorder()
		customLogger := slog.New(slog.NewTextHandler(recorder, nil))

		presenter := presenters.NewDesignMutationOutcomePresenter(customLogger, httptest.NewRecorder())

		// This should log the error
		presenter.PresentError(errors.New("test error"))

		// The presenter should work normally regardless of logger configuration
		assert.NotNil(t, presenter)
	})
}
