package presenters_test

import (
	"bytes"
	"errors"
	"log/slog"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// Test helper functions for common testing patterns

// CreateTestLogger creates a logger suitable for testing
func CreateTestLogger() *slog.Logger {
	return slog.Default()
}

// CreateBufferedLogger creates a logger that writes to a buffer for testing log output
func CreateBufferedLogger() (*slog.Logger, *bytes.Buffer) {
	var buf bytes.Buffer
	logger := slog.New(slog.NewTextHandler(&buf, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	return logger, &buf
}

// CreateSilentLogger creates a logger that discards all output
func CreateSilentLogger() *slog.Logger {
	return slog.New(slog.NewTextHandler(bytes.NewBuffer(nil), &slog.HandlerOptions{
		Level: slog.LevelError + 1, // Higher than any log level to silence all logs
	}))
}

// Mock ResponseWriter that can simulate write failures
type MockFailingResponseWriter struct {
	*httptest.ResponseRecorder
	ShouldFailOnWrite bool
	WriteError        error
}

func NewMockFailingResponseWriter() *MockFailingResponseWriter {
	return &MockFailingResponseWriter{
		ResponseRecorder: httptest.NewRecorder(),
		WriteError:       errors.New("mock write failure"),
	}
}

func (m *MockFailingResponseWriter) Write(data []byte) (int, error) {
	if m.ShouldFailOnWrite {
		return 0, m.WriteError
	}
	return m.ResponseRecorder.Write(data)
}

// Test data generators

// GenerateTestDesign creates a test design with all fields populated
func GenerateTestDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lastUpdated := "2025-06-13T00:59:59Z"
	none := usecases.NoWallpaper
	halfWall := usecases.HalfWall
	defaultFloorTilePattern := usecases.HorizontalStacked
	nope := false
	return adapters.Design{
		ID:                   uuid.NewString(),
		FloorTile:            &floorTile,
		Toilet:               &toilet,
		Vanity:               &vanity,
		Faucet:               &faucet,
		Mirror:               &mirror,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &none,
		WallTilePlacement:    &halfWall,
		FloorTilePattern:     &defaultFloorTilePattern,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
}

// GenerateMinimalTestDesign creates a test design with only required fields
func GenerateMinimalTestDesign() adapters.Design {
	return adapters.Design{
		ID: uuid.NewString(),
	}
}

// GenerateTestUsecaseDesign creates a test usecase design
func GenerateTestUsecaseDesign(projectId entities.ProjectId) usecases.Design {
	design := GenerateTestDesign()
	usecaseDesign, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		panic("Failed to create test usecase design: " + err.Error())
	}
	return usecaseDesign
}

// Helper functions for creating pointers to tile patterns
func TilePatternPtr(pattern usecases.TilePattern) *usecases.TilePattern {
	return &pattern
}

// Helper functions for creating test project IDs
func NewTestProjectID(suffix string) entities.ProjectId {
	return entities.ProjectId("PRJ-TEST-" + suffix)
}

// Assertion helpers

// AssertHTTPResponse checks common HTTP response properties
func AssertHTTPResponse(t *testing.T, recorder *httptest.ResponseRecorder, expectedStatus int, expectedBody string) {
	t.Helper()
	assert.Equal(t, expectedStatus, recorder.Code, "HTTP status code mismatch")
	assert.Equal(t, expectedBody, recorder.Body.String(), "HTTP response body mismatch")
}

// AssertCORSHeaders checks that CORS headers are set correctly
func AssertCORSHeaders(t *testing.T, recorder *httptest.ResponseRecorder) {
	t.Helper()
	assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"), "CORS header not set correctly")
}

// AssertJSONContentType checks that the content type is set to JSON
func AssertJSONContentType(t *testing.T, recorder *httptest.ResponseRecorder) {
	t.Helper()
	assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"), "Content-Type header not set to JSON")
}

// AssertLocationHeader checks that the Location header is set correctly
func AssertLocationHeader(t *testing.T, recorder *httptest.ResponseRecorder, projectId entities.ProjectId, designId uuid.UUID) {
	t.Helper()
	expectedLocation := "/projects/" + string(projectId) + "/designs/" + designId.String()
	assert.Equal(t, expectedLocation, recorder.Header().Get("Location"), "Location header not set correctly")
}

// Test data for boundary cases

// UnmarshalableData creates data that cannot be marshaled to JSON (circular reference)
func UnmarshalableData() interface{} {
	type circular struct {
		Self *circular `json:"self"`
	}
	data := &circular{}
	data.Self = data
	return data
}

// Error generators for testing

// CommonTestErrors provides common test errors
var CommonTestErrors = struct {
	Generic    error
	Empty      error
	Database   error
	Network    error
	Validation error
}{
	Generic:    errors.New("generic test error"),
	Empty:      errors.New(""),
	Database:   errors.New("database connection failed"),
	Network:    errors.New("network timeout"),
	Validation: errors.New("validation failed"),
}

// Edge case data generators

// EdgeCaseProjectIDs provides project IDs for edge case testing
var EdgeCaseProjectIDs = struct {
	Empty       entities.ProjectId
	WithSlashes entities.ProjectId
	WithPercent entities.ProjectId
	WithSpaces  entities.ProjectId
	VeryLong    entities.ProjectId
}{
	Empty:       entities.ProjectId(""),
	WithSlashes: entities.ProjectId("PRJ-123/456/789"),
	WithPercent: entities.ProjectId("PRJ-123%456%789"),
	WithSpaces:  entities.ProjectId("PRJ 123 456"),
	VeryLong:    entities.ProjectId("PRJ-" + string(make([]byte, 1000))),
}

// EdgeCaseUUIDs provides UUIDs for edge case testing
var EdgeCaseUUIDs = struct {
	Zero uuid.UUID
	Max  uuid.UUID
}{
	Zero: uuid.UUID{},
	Max:  uuid.UUID{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
}

// Test table helpers

// ErrorTestCase represents a test case for error handling
type ErrorTestCase struct {
	Name           string
	Error          error
	ExpectedStatus int
	ExpectedBody   string
}

// SuccessTestCase represents a test case for success scenarios
type SuccessTestCase struct {
	Name           string
	ProjectID      *entities.ProjectId
	DesignID       *uuid.UUID
	ExpectedStatus int
	ExpectedBody   string
}

// BoundaryTestCase represents a test case for boundary conditions
type BoundaryTestCase struct {
	Name        string
	Input       interface{}
	ExpectedOut interface{}
	ShouldPanic bool
}
