package controllers_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/openai/openai-go"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

// TODO: move memStore to a separate package that can be shared with other tests.
type memStore struct {
	designs map[entities.ProjectId][]adapters.Design
}

func newMemStore() *memStore {
	m := memStore{
		designs: make(map[entities.ProjectId][]adapters.Design),
	}
	return &m
}

func (m *memStore) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	results, ok := m.designs[projectId]
	if !ok {
		return nil, fmt.Errorf("project %s not found", projectId)
	}
	return results, nil
}

func (m *memStore) DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, []error, error) {
	results := make(map[entities.ProjectId][]adapters.Design)
	errors := []error{}
	for _, projectId := range projectIds {
		designs, err := m.DesignsForProject(ctx, projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (m *memStore) addDesignToProject(projectId entities.ProjectId, design adapters.Design) error {
	m.designs[projectId] = append(m.designs[projectId], design)
	return nil
}

func (m *memStore) UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error {
	if _, ok := m.designs[projectId]; !ok {
		return fmt.Errorf("project %s not found", projectId)
	}
	m.designs[projectId] = designs
	return nil
}

func (m *memStore) MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error {
	return nil
}

func (m *memStore) IdsOfProjectsWithDesigns(_ context.Context) ([]entities.ProjectId, error) {
	var projectIds []entities.ProjectId
	for projectId := range m.designs {
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

const (
	projId             = "PRJ-FOOBAR"
	jsonSchemaFilename = "../../room-design.schema.json"
)

func setup(t *testing.T) (*controllers.DesignMutationController, *memStore, adapters.Design) {
	t.Helper()
	m := newMemStore()
	tg := gateways.NewOpenAI(openai.NewClient())
	handler := controllers.NewDesignMutationController(m, tg, nil)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	if err := m.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	return handler, m, testDesign
}

func TestAddingDesign(t *testing.T) {
	handler, store, _ := setup(t)
	testDesign2 := genDesign()
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.CreateDesign(t.Context(), projId, testDesign2, presenter)
	if status := recorder.Code; status != http.StatusCreated {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	locationHeader := recorder.Header().Get("Location")
	if locationHeader == "" {
		t.Error("Location header not set")
	} else {
		expectedLocationPrefix := fmt.Sprintf("/projects/%s/designs/", projId)
		if !strings.HasPrefix(locationHeader, expectedLocationPrefix) {
			t.Errorf("Location header prefix mismatch: %s should begin with %s", locationHeader, expectedLocationPrefix)
		}
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, 2)
	}
}

func TestModifyingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	testDesign.Tags = 1
	vis := true
	testDesign.IsShowerGlassVisible = &vis
	patch := adapters.Design{
		ID:                   testDesign.ID,
		WallpaperPlacement:   &vanityWall,
		Tags:                 1,
		IsShowerGlassVisible: &vis,
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.ModifyDesign(t.Context(), projId, patch, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}
func TestReplacingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveDesign(t.Context(), projId, testDesign, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}

func TestDeletingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.DeleteDesign(t.Context(), projId, uuid.MustParse(testDesign.ID), presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 0; got != want {
		t.Errorf("wrong number of designs: got %d want %d", got, want)
	}
}
